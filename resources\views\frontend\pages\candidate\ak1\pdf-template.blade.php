<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AK1 - {{ $user->name }}</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 10mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.1;
            margin: 0;
            padding: 0;
            width: 100%;
        }

        .container {
            width: 100%;
            height: 100%;
            display: table;
        }

        .header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
        }

        .header-left {
            display: table-cell;
            width: 140px;
            vertical-align: middle;
        }

        .logo {
            width: 60px;
            height: 60px;
            float: right;
            margin-right: 10px;
        }

        .logo img {
            width: 60px;
            height: 60px;
            object-fit: contain;
        }

        .header-middle {
            display: table-cell;
            vertical-align: middle;
            text-align: center;
        }

        .header-right {
            display: table-cell;
            width: 200px;
            vertical-align: top;
            text-align: right;
        }

        .header h1 {
            font-size: 12px;
            font-weight: bold;
            margin: 0;
            text-transform: uppercase;
        }

        .header h2 {
            font-size: 10px;
            font-weight: bold;
            margin: 2px 0;
            text-transform: uppercase;
        }
        
        .card-label {
            font-size: 10px;
            font-weight: bold;
        }
        
        .content {
            display: table;
            width: 100%;
            margin-top: 5px;
        }

        .left-section {
            display: table-cell;
            width: 140px;
            vertical-align: top;
            padding-right: 15px;
            padding-top: 35px;
        }

        .middle-section {
            display: table-cell;
            vertical-align: top;
            padding-right: 15px;
            padding-top: 25px;
        }

        .right-section {
            display: table-cell;
            width: 200px;
            vertical-align: top;
            padding-top: 25px;
        }

        .photo-box {
            width: 100px;
            height: 120px;
            border: 0px solid #000;
            margin-bottom: 8px;
            text-align: center;
            background-color: #f5f5f5;
            font-size: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .photo-box img {
            width: 98px;
            height: 118px;
            object-fit: cover;
            object-position: center;
        }

        .signature-box {
            width: 100px;
            height: 50px;
            border: 1px solid #000;
            text-align: center;
            background-color: #f5f5f5;
            font-size: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .signature-box img {
            width: 98px;
            height: 48px;
            object-fit: contain;
            object-position: center;
        }

        .barcode-container {
            text-align: center;
            margin-top: 20px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #f9f9f9;
        }

        .barcode-wrapper {
            display: inline-block;
            text-align: center;
            margin-bottom: 8px;
        }

        .barcode-image {
            width: 80px;
            height: 80px;
            display: block;
            margin: 0 auto;
            border: 2px solid #fff;
            border-radius: 4px;
        }

        .barcode-info {
            margin-top: 8px;
        }

        .verification-date {
            font-size: 8px;
            margin-bottom: 2px;
            text-align: center;
            font-weight: bold;
            color: #333;
        }

        .validity-period {
            font-size: 7px;
            text-align: center;
            font-style: italic;
            color: #666;
        }

        .verification-url {
            font-size: 6px;
            margin-top: 3px;
            text-align: center;
            color: #888;
            word-break: break-all;
        }

        .barcode-container {
            text-align: center;
            margin-top: 10px;
        }

        .barcode-image {
            width: 100px;
            height: 100px;
            margin: 0 auto;
        }

        .verification-date {
            font-size: 7px;
            margin-top: 5px;
            text-align: center;
        }

        .validity-period {
            font-size: 7px;
            margin-top: 2px;
            text-align: center;
            font-style: italic;
        }
        
        .form-row {
            margin-bottom: 5px;
            display: table;
            width: 100%;
        }

        .form-label {
            display: table-cell;
            width: 35%;
            font-weight: bold;
            vertical-align: middle;
            padding-right: 3px;
            font-size: 9px;
        }

        .form-value {
            display: table-cell;
            width: 65%;
            border: 1px solid #000;
            padding: 3px 5px;
            vertical-align: middle;
            font-size: 9px;
            min-height: 20px;
        }

        .form-value.no-border {
            border: none;
            padding: 2px 0;
        }

        .registration-number {
            margin-bottom: 8px;
            font-size: 9px;
            display: table;
            width: 100%;
        }

        .registration-label {
            display: table-cell;
            width: 35%;
            font-weight: bold;
            vertical-align: middle;
            padding-right: 3px;
            font-size: 9px;
        }

        .registration-value {
            display: table-cell;
            width: 65%;
            vertical-align: middle;
        }

        .number-boxes {
            display: block;
            width: 100%;
            margin-top: 3px;
        }

        .number-box {
            display: inline-block;
            width: 18px;
            height: 22px;
            border: 1px solid #000;
            text-align: center;
            line-height: 22px;
            margin: 0 1px;
            font-weight: bold;
            font-size: 9px;
        }

        .number-group {
            display: inline-block;
            margin: 0 8px;
        }

        .section-title {
            font-weight: bold;
            text-transform: uppercase;
            margin: 0 0 5px 0;
            text-align: left;
            border-bottom: 1px solid #000;
            padding-bottom: 2px;
            font-size: 9px;
        }

        .section-title.right-align {
            text-align: right;
        }

        .info-box {
            border: 1px solid #000;
            min-height: 50px;
            padding: 3px;
            margin-bottom: 5px;
            font-size: 8px;
        }

        .footer {
            margin-top: 15px;
            text-align: right;
        }

        .footer-content {
            display: inline-block;
            text-align: center;
            width: 150px;
            font-size: 8px;
        }

        .education-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }

        .education-table th,
        .education-table td {
            border: 1px solid #000;
            padding: 3px 5px;
            text-align: center;
            font-size: 8px;
        }

        .education-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6e/Lambang_Kota_Tangerang_Selatan.svg/1050px-Lambang_Kota_Tangerang_Selatan.svg.png" alt="Logo Tangerang Selatan">
            </div>
        </div>
        <div class="header-middle">
            <h1>DINAS TENAGA KERJA KOTA TANGERANG SELATAN</h1>
            <h2>KARTU TANDA BUKTI PENDAFTARAN PENCARI KERJA</h2>
        </div>
        <div class="header-right">
            <div class="card-label">Kartu AK/I</div>
        </div>
    </div>
    
    <div class="content">
        <div class="left-section">
            <div class="photo-box">
                @php
                    $photoPath = null;
                    if($application->photo_file && file_exists(storage_path('app/public/' . $application->photo_file))) {
                        $photoPath = storage_path('app/public/' . $application->photo_file);
                    } elseif($user->candidate && $user->candidate->photo && file_exists(public_path($user->candidate->photo))) {
                        $photoPath = public_path($user->candidate->photo);
                    }
                @endphp
                @if($photoPath)
                    @php
                        $imageData = base64_encode(file_get_contents($photoPath));
                        $mimeType = mime_content_type($photoPath);
                    @endphp
                    <img src="data:{{ $mimeType }};base64,{{ $imageData }}" alt="Foto">
                @else
                    FOTO
                @endif
            </div>

            <div style="margin-top: 10px;"></div>

            <div class="signature-box">
                @php
                    $signaturePath = null;
                    if($application->signature_file && file_exists(storage_path('app/public/' . $application->signature_file))) {
                        $signaturePath = storage_path('app/public/' . $application->signature_file);
                    }
                @endphp
                @if($signaturePath)
                    @php
                        $signatureData = base64_encode(file_get_contents($signaturePath));
                        $signatureMimeType = mime_content_type($signaturePath);
                    @endphp
                    <img src="data:{{ $signatureMimeType }};base64,{{ $signatureData }}" alt="Tanda Tangan">
                @else
                    TTD
                @endif
            </div>

            <div class="barcode-container">
                @php
                    // Create verification URL dynamically
                    $verificationUrl = url('/verifikasi-ak1?no_reg=' . $application->registration_number);

                    // Generate QR code with high error correction
                    $barcodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=' . urlencode($verificationUrl) . '&ecc=H&format=png&margin=3';

                    // Format tanggal Indonesia
                    $verifiedDate = $application->verified_at ? $application->verified_at : now();
                    $months = [
                        1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
                        5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
                        9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
                    ];
                    $day = $verifiedDate->format('j');
                    $month = $months[(int)$verifiedDate->format('n')];
                    $year = $verifiedDate->format('Y');
                    $formattedDate = "$day $month $year";

                    // Tanggal berlaku hingga (2 tahun dari verifikasi)
                    $validUntil = $verifiedDate->copy()->addYears(2);
                    $validDay = $validUntil->format('j');
                    $validMonth = $months[(int)$validUntil->format('n')];
                    $validYear = $validUntil->format('Y');
                    $validUntilFormatted = "$validDay $validMonth $validYear";
                @endphp

                <div class="barcode-wrapper">
                    <img src="{{ $barcodeUrl }}" alt="QR Code Verifikasi" class="barcode-image">
                </div>

                <div class="barcode-info">
                    <div class="verification-date">
                        Terdaftar: {{ $formattedDate }}
                    </div>
                    <div class="validity-period">
                        Berlaku hingga: {{ $validUntilFormatted }}
                    </div>
                    <div class="verification-url">
                        Scan untuk verifikasi online
                    </div>
                </div>
            </div>
        </div>
        
        <div class="middle-section">
            <div class="registration-number">
                <div class="registration-label">No. Pendaftaran Pencari Kerja</div>
                <div class="registration-value">
                    <div class="number-boxes">
                        @php
                            $regNumber = $application->registration_number;
                            $group1 = substr($regNumber, 0, 4); // 3674
                            $group2 = substr($regNumber, 4, 6); // DDMMYY
                            $group3 = substr($regNumber, 10, 6); // 6-digit sequence
                        @endphp

                        <div class="number-group">
                            @foreach(str_split($group1) as $digit)
                                <span class="number-box">{{ $digit }}</span>
                            @endforeach
                        </div>

                        <div class="number-group">
                            @foreach(str_split($group2) as $digit)
                                <span class="number-box">{{ $digit }}</span>
                            @endforeach
                        </div>

                        <div class="number-group">
                            @foreach(str_split($group3) as $digit)
                                <span class="number-box">{{ $digit }}</span>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-label">No. Induk Kependudukan</div>
                <div class="form-value">{{ $user->nik }}</div>
            </div>

            <div class="form-row">
                <div class="form-label">NAMA LENGKAP</div>
                <div class="form-value">{{ strtoupper($user->name) }}</div>
            </div>

            <div class="form-row">
                <div class="form-label">TEMPAT/TGL LAHIR</div>
                <div class="form-value">{{ strtoupper($user->tempat_lahir) }} / {{ $user->tanggal_lahir ? date('d/m/Y', strtotime($user->tanggal_lahir)) : '' }}</div>
            </div>

            <div class="form-row">
                <div class="form-label">JENIS KELAMIN</div>
                <div class="form-value">{{ strtoupper($user->jenis_kelamin) }}</div>
            </div>

            <div class="form-row">
                <div class="form-label">STATUS</div>
                <div class="form-value">{{ strtoupper($user->status_perkawinan) }}</div>
            </div>

            <div class="form-row">
                <div class="form-label">AGAMA</div>
                <div class="form-value">{{ strtoupper($user->agama) }}</div>
            </div>

            <div class="form-row">
                <div class="form-label">ALAMAT</div>
                <div class="form-value no-border">{{ strtoupper($user->alamat_ktp) }}</div>
            </div>
        </div>
        
        <div class="right-section">
            <div class="section-title">PENDIDIKAN FORMAL</div>
            <table class="education-table">
                <thead>
                    <tr>
                        <th>Tingkat Pendidikan</th>
                        <th>Jurusan</th>
                        <th>Lulus Tahun</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ $user->candidate && $user->candidate->education ? $user->candidate->education->name : $user->pendidikan_terakhir }}</td>
                        <td>{{ $user->candidate && $user->candidate->educations->first() ? $user->candidate->educations->first()->subject : 'IPS' }}</td>
                        <td>{{ $user->candidate && $user->candidate->educations->first() ? date('Y', strtotime($user->candidate->educations->first()->year)) : '2020' }}</td>
                    </tr>
                </tbody>
            </table>

            <div class="section-title">KETERAMPILAN</div>
            <div class="info-box">
                @if($user->candidate && $user->candidate->skills->count() > 0)
                    @foreach($user->candidate->skills as $skill)
                        • {{ $skill->name }}<br>
                    @endforeach
                @endif
            </div>

            <div class="section-title">PENGALAMAN KERJA</div>
            <div class="info-box">
                @if($user->candidate && $user->candidate->experiences->count() > 0)
                    @foreach($user->candidate->experiences->take(3) as $experience)
                        • {{ $experience->designation }} di {{ $experience->company }}<br>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
    
    <div class="footer">
        <div class="footer-content">
            <div style="margin-bottom: 50px;">Pengantar Kerja/Petugas Antar Kerja</div>
            <div style="border-bottom: 1px solid #000; margin-bottom: 5px;"></div>
            <div>{{ $application->verifiedBy ? $application->verifiedBy->name : 'Admin' }}</div>
            @if($application->verifiedBy && $application->verifiedBy->nip)
                <div>NIP. {{ $application->verifiedBy->nip }}</div>
            @endif
        </div>
    </div>
</body>
</html>
