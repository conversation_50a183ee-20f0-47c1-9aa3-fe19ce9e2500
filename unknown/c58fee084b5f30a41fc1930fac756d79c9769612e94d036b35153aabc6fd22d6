<?php

namespace App\Services\Admin\Company;

use App\Notifications\UpdateCompanyPassNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class CompanyUpdateService
{
    /**
     * Update company
     */

    // public function execute($request, $company): void
    // {
    //     // update user
    //     $company = $company->user;
    //     $data['name'] = $request->name ?? fake()->name();
    //     $data['email'] = $request->email;
    //     $data['username'] = $request->username ?? Str::slug($data['name']).'_'.time();

    //     if ($request->password) {
    //         $data['password'] = bcrypt($request->password);
    //     }

    //     $company->update($data);

    //     // update company
    //     $company->company()->update([
    //         'industry_type_id' => $request->industry_type_id,
    //         'organization_type_id' => $request->organization_type_id,
    //         'team_size_id' => $request->team_size_id,
    //         'establishment_date' => Carbon::parse($request->establishment_date)->format('Y-m-d') ?? null,
    //         'website' => $request->website,
    //         'bio' => $request->bio,
    //         'vision' => $request->vision,
    //     ]);

    //     // update logo
    //     if ($request->logo) {

    //         $logo_url = uploadFileToPublic($request->logo, 'company');
    //         $company->company()->update(['logo' => $logo_url]);
    //     }

    //     // update banner
    //     if ($request->image) {
    //         $banner_url = uploadFileToPublic($request->image, 'company');
    //         $company->company()->update(['banner' => $banner_url]);
    //     }

    //     // update contact info
    //     $company->contactInfo()->update([
    //         'phone' => $request->contact_phone,
    //         'email' => $request->contact_email,
    //     ]);

    //     // Social media update
    //     $company->socialInfo()->delete();

    //     $social_medias = $request->social_media;
    //     $urls = $request->url;

    //     foreach ($social_medias as $key => $value) {
    //         if ($value && $urls[$key]) {
    //             $company->socialInfo()->create([
    //                 'social_media' => $value ?? '',
    //                 'url' => $urls[$key] ?? '',
    //             ]);
    //         }
    //     }

    //     // Location
    //     updateMap($company->company());

    //     // Send mail notification
    //     $this->sendMailNotification($request, $company);
    // }

    public function execute($request, $company): void
    {
        // Debugging
        \Log::info('CompanyUpdateService: Executing update', [
            'company' => $company ? 'exists' : 'null',
            'company_id' => $company ? $company->id : 'null',
            'company_class' => $company ? get_class($company) : 'null',
            'has_user' => $company && $company->user ? 'yes' : 'no',
            'request_method' => $request->method(),
            'request_path' => $request->path()
        ]);

        // Update user data jika user ada
        if ($company && $company->user) {
            $user = $company->user;
            $data['name'] = $request->name;
            $data['email'] = $request->email;
            $data['username'] = $request->username ?? Str::slug($data['name']).'_'.time();

            if ($request->password) {
                $data['password'] = bcrypt($request->password);
            }

            $user->update($data);
        } else {
            \Log::warning('CompanyUpdateService: User tidak ditemukan, hanya memperbarui data perusahaan');
        }

        // update company
        if ($company) {
            // Debugging establishment_year
            \Log::info('CompanyUpdateService: establishment_year', [
                'establishment_year' => $request->establishment_year,
                'all_data' => $request->all()
            ]);

            $establishment_date = null;
            if ($request->establishment_year) {
                $establishment_date = $request->establishment_year . '-01-01';
            }

            $company->update([
                'industry_type_id' => $request->industry_type_id,
                'organization_type_id' => $request->organization_type_id,
                'team_size_id' => $request->team_size_id,
                'establishment_date' => $establishment_date,
                'website' => $request->website,
                'bio' => $request->bio,
                'vision' => $request->vision,
            ]);

            // update logo
            if ($request->logo) {
                if ($company->logo) {
                    deleteImage($company->logo);
                }
                $path = 'uploads/images/company';
                $logo_url = uploadImage($request->logo, $path, [68, 68]);

                $company->update(['logo' => $logo_url]);
            }

            // update banner
            if ($request->image) {
                if ($company->banner) {
                    deleteImage($company->banner);
                }
                $path = 'uploads/images/company';
                $banner_url = uploadImage($request->image, $path, [1920, 312]);

                $company->update(['banner' => $banner_url]);
            }

            // Location
            // Simpan kecamatan dan kelurahan ID jika ada
            if ($request->kecamatan_id) {
                $company->kecamatan_id = $request->kecamatan_id;

                // Ambil nama kecamatan
                $kecamatan = \App\Models\Kecamatan::find($request->kecamatan_id);
                if ($kecamatan) {
                    $company->locality = $kecamatan->name;
                }
            }

            if ($request->kelurahan_id) {
                $company->kelurahan_id = $request->kelurahan_id;

                // Ambil nama kelurahan
                $kelurahan = \App\Models\Kelurahan::find($request->kelurahan_id);
                if ($kelurahan) {
                    $company->neighborhood = $kelurahan->name;
                }
            }

            // Simpan perubahan
            $company->save();

            // Update map dengan data yang sudah diperbarui
            updateMap($company);
        }

        // Update user related data jika user ada
        if ($company && $company->user) {
            $user = $company->user;

            // update contact info
            $user->contactInfo()->update([
                'phone' => $request->contact_phone,
                'email' => $request->contact_email,
            ]);

            // Social media update
            $user->socialInfo()->delete();

            $social_medias = $request->social_media;
            $urls = $request->url;

            foreach ($social_medias as $key => $value) {
                if ($value && $urls[$key]) {
                    $user->socialInfo()->create([
                        'social_media' => $value ?? '',
                        'url' => $urls[$key] ?? '',
                    ]);
                }
            }

            // Send mail notification
            $this->sendMailNotification($request, $user);
        }
    }

    /**
     * Send mail notification
     */
    protected function sendMailNotification($request, $user): void
    {
        try {
            if ($request->password && $user && $user->email) {
                $data[] = $user;
                $data[] = $request->password;
                $data[] = 'Company';

                checkMailConfig() ? Notification::route('mail', $user->email)->notify(new UpdateCompanyPassNotification($data)) : '';
            }
        } catch (\Exception $e) {
            \Log::error('CompanyUpdateService: Error sending notification', [
                'error' => $e->getMessage()
            ]);
            // Tidak melempar exception agar proses update tetap berjalan
        }
    }
}
