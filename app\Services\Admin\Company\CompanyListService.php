<?php

namespace App\Services\Admin\Company;

use App\Models\Company;
use App\Models\Job;

class CompanyListService
{
    /**
     * Get company list
     */
    public function execute($request): mixed
    {
        $query = Company::query();

        // sortby status
        if ($request->sort_by == 'latest' || $request->sort_by == null) {
            $query->latest();
        } else {
            $query->oldest();
        }

        // verified status
        if ($request->has('ev_status') && $request->ev_status != null) {
            if ($request->ev_status == 'true') {
                $query->whereHas('user', function ($q) {
                    $q->whereNotNull('email_verified_at');
                });
            } else {
                $query->whereHas('user', function ($q) {
                    $q->whereNull('email_verified_at');
                });
            }
        }

        if ($request->keyword && $request->keyword != null) {
            $query->whereLike(['user.name', 'user.email'], $request->keyword);
        }

        // organization type filter
        if ($request->organization_type && $request->organization_type != null) {
            $query->where('organization_type_id', $request->organization_type);
        }

        // industry type filter
        if ($request->industry_type && $request->industry_type != null) {
            $query->where('industry_type_id', $request->industry_type);
        }

        // Filter berdasarkan status user (aktif/tidak aktif)
        if ($request->has('user_status')) {
            $query->whereHas('user', function($q) use ($request) {
                $q->where('status', $request->user_status);
            });
        }

        // Filter perusahaan dengan loker aktif
        if ($request->has('with_jobs') && $request->with_jobs) {
            $query->whereHas('jobs', function($q) {
                $q->where('status', 'active')
                  ->where('deadline', '>=', now()->toDateString());
            });
        }

        // Debugging
        \Log::info('CompanyListService: Executing query', [
            'query_sql' => $query->toSql(),
            'query_bindings' => $query->getBindings()
        ]);

        $companies = $query->with(['organization.translations', 'user', 'industry'])->paginate(10)->through(function ($company) {
            if ($company && $company->id) {
                $company->active_jobs = Job::where('company_id', $company->id)->openPosition()->count();
            } else {
                $company->active_jobs = 0;
                \Log::warning('CompanyListService: Company is null or has no ID in through function');
            }

            // Debugging
            \Log::info('CompanyListService: Company data', [
                'company_id' => $company ? $company->id : 'null',
                'name' => $company && $company->user ? $company->user->name : 'No user',
                'industry_type_id' => $company ? $company->industry_type_id : 'null',
                'industry_name' => $company && $company->industry ? $company->industry->name : 'No industry',
                'organization_type_id' => $company ? $company->organization_type_id : 'null',
                'organization_name' => $company && $company->organization ? $company->organization->name : 'No organization',
                'establishment_date' => $company ? $company->establishment_date : 'null'
            ]);

            return $company;
        });

        return $companies;
    }
}
