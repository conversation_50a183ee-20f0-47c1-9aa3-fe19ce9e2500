@extends('frontend.layouts.app')

@section('title', 'Verifikasi AK1')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-certificate me-2"></i>
                        Verifikasi Kartu AK1
                    </h4>
                </div>
                <div class="card-body">
                    @if(isset($showForm) && $showForm)
                        <!-- Form Input Nomor Registrasi -->
                        <div class="text-center mb-4">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6e/Lambang_Kota_Tangerang_Selatan.svg/1050px-Lambang_Kota_Tangerang_Selatan.svg.png"
                                 alt="Logo Tangerang Selatan"
                                 style="width: 80px; height: 80px;">
                            <h5 class="mt-3 mb-0">DINAS TENAGA KERJA KOTA TANGERANG SELATAN</h5>
                            <p class="text-muted">Verifikasi Kartu Tanda Bukti Pendaftaran Pencari Kerja</p>
                        </div>

                        @if(isset($error))
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {{ $error }}
                                @if(isset($searchedNumber))
                                    <br><small>Nomor yang dicari: <strong>{{ $searchedNumber }}</strong></small>
                                @endif
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {{ session('error') }}
                            </div>
                        @endif

                        <form action="{{ route('ak1.verification.post') }}" method="POST" class="mb-4">
                            @csrf
                            <div class="mb-3">
                                <label for="no_reg" class="form-label">
                                    <strong>Nomor Pendaftaran Pencari Kerja</strong>
                                </label>
                                <input type="text"
                                       class="form-control form-control-lg text-center"
                                       id="no_reg"
                                       name="no_reg"
                                       placeholder="Contoh: 3674120725000100"
                                       value="{{ old('no_reg', $searchedNumber ?? '') }}"
                                       required
                                       style="letter-spacing: 2px; font-family: monospace;">
                                <div class="form-text">
                                    Masukkan nomor pendaftaran AK1 yang tertera pada kartu Anda
                                </div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-search me-2"></i>
                                    Verifikasi Sekarang
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="text-muted small">
                                <i class="fas fa-info-circle me-1"></i>
                                Nomor pendaftaran dapat ditemukan pada kartu AK1 Anda atau melalui QR Code
                            </p>
                            <a href="{{ route('website.home') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>
                                Kembali ke Beranda
                            </a>
                        </div>
                    @else
                        <div class="text-center mb-4">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6e/Lambang_Kota_Tangerang_Selatan.svg/1050px-Lambang_Kota_Tangerang_Selatan.svg.png" 
                                 alt="Logo Tangerang Selatan" 
                                 style="width: 80px; height: 80px;">
                            <h5 class="mt-3 mb-0">DINAS TENAGA KERJA KOTA TANGERANG SELATAN</h5>
                            <p class="text-muted">Kartu Tanda Bukti Pendaftaran Pencari Kerja</p>
                        </div>

                        <div class="alert alert-success text-center">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Kartu AK1 Valid dan Terverifikasi</strong>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Nomor Registrasi:</strong></td>
                                        <td>{{ $application->registration_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Nama Lengkap:</strong></td>
                                        <td>{{ $application->user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>NIK:</strong></td>
                                        <td>{{ $application->user->nik }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tempat, Tgl Lahir:</strong></td>
                                        <td>{{ $application->user->tempat_lahir }}, {{ date('d/m/Y', strtotime($application->user->tanggal_lahir)) }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>
                                                Terverifikasi
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tanggal Verifikasi:</strong></td>
                                        <td>{{ $formattedDate }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Berlaku Hingga:</strong></td>
                                        <td>{{ $validUntilFormatted }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Diverifikasi oleh:</strong></td>
                                        <td>{{ $application->verifiedBy ? $application->verifiedBy->name : 'Admin' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        @if($application->notes)
                            <div class="alert alert-info">
                                <strong>Catatan:</strong><br>
                                {{ $application->notes }}
                            </div>
                        @endif

                        <div class="text-center mt-4">
                            <p class="text-muted small">
                                <i class="fas fa-info-circle me-1"></i>
                                Kartu AK1 ini telah diverifikasi secara resmi oleh Dinas Tenaga Kerja Kota Tangerang Selatan
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('css')
<style>
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        background: linear-gradient(135deg, #007bff, #0056b3);
    }

    .table td {
        padding: 0.5rem 0.75rem;
        border: none;
    }

    .badge {
        font-size: 0.9em;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .alert {
        border-radius: 10px;
        border: none;
    }

    .form-control-lg {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control-lg:focus {
        border-color: #007bff;
        transform: scale(1.02);
    }
</style>
@endsection
